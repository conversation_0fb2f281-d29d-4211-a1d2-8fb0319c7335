{"name": "banking_app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "chart.js": "^4.4.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.424.0", "next": "14.2.5", "query-string": "^9.1.0", "react": "^18", "react-chartjs-2": "^5.2.0", "react-countup": "^6.5.3", "react-dom": "^18", "react-plaid-link": "^3.6.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}