const { spawn } = require('child_process');
const path = require('path');

// Try to find Next.js binary
const nextPaths = [
  'node_modules/.next-dbDrQx1A/dist/bin/next',
  'node_modules/next/dist/bin/next',
  'node_modules/@next/env/dist/bin/next'
];

let nextPath = null;
const fs = require('fs');

for (const p of nextPaths) {
  if (fs.existsSync(p)) {
    nextPath = p;
    break;
  }
}

if (!nextPath) {
  console.error('Next.js binary not found!');
  process.exit(1);
}

console.log(`Starting Next.js development server using: ${nextPath}`);

const child = spawn('node', [nextPath, 'dev'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

child.on('error', (error) => {
  console.error('Error starting Next.js:', error);
});

child.on('close', (code) => {
  console.log(`Next.js process exited with code ${code}`);
});
